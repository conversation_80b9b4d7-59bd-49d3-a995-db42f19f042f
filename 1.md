# ofono dbus-send命令获取设备信息指南

## 基本命令

### 获取所有调制解调器路径
```bash
dbus-send --system --print-reply --dest=org.ofono / org.ofono.Manager.GetModems
```
**说明**：此命令不需要参数，返回所有可用的调制解调器路径，如`/ril_0`、`/ril_1`等。

### 获取调制解调器的所有属性（包含序列号）
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.Modem.GetProperties
```
**参数说明**：
- `[调制解调器路径]`：替换为实际路径，例如：`/ril_0`

**示例**：
```bash
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.Modem.GetProperties
```

### 获取IMEI信息（通过过滤所有属性）
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.Modem.GetProperties | grep -A 1 "Serial\|IMEI"
```
**参数说明**：同上

### 查询YSIMEI
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.Modem.QueryYSIMEI
```
**参数说明**：同上

### 获取网络注册信息（可能包含IMSI等）
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.NetworkRegistration.GetProperties
```
**参数说明**：同上

### 获取SIM卡信息（可能包含ICCID等）
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.SimManager.GetProperties
```
**参数说明**：同上

## SIM卡状态与切换命令

### 获取SIM卡状态
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.SimManager.GetProperties | grep -A 1 "Present\|PinRequired\|SubscriberIdentity\|CardIdentifier"
```
**参数说明**：同上

**返回值说明**：
- `Present`：布尔值，表示SIM卡是否存在
- `PinRequired`：字符串，表示需要的PIN类型（"none"、"pin"、"puk"等）
- `SubscriberIdentity`：字符串，IMSI号码
- `CardIdentifier`：字符串，ICCID号码

### 获取SIM卡插槽模式
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.SimManager.QuerySlotMode
```
**参数说明**：同上

**返回值说明**：
- 返回当前的插槽模式，通常是一个整数，表示不同的SIM卡配置

### 切换SIM卡插槽模式
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.SimManager.SwitchSlotMode uint32:[模式]
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`
- `[模式]`：整数值，表示插槽模式
  - `0`：通常表示单卡模式，仅使用卡槽1
  - `1`：通常表示单卡模式，仅使用卡槽2
  - `2`：通常表示双卡模式，同时使用两个卡槽

**示例**：
```bash
# 切换到双卡模式
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.SimManager.SwitchSlotMode uint32:2
```

### 切换SIM卡
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.SimManager.SwitchSimcard uint32:[卡槽ID]
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`
- `[卡槽ID]`：整数值，表示要切换到的卡槽ID
  - `0`：通常表示卡槽1
  - `1`：通常表示卡槽2

**示例**：
```bash
# 切换到卡槽2
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.SimManager.SwitchSimcard uint32:1
```

### 设置SIM卡电源状态
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.SimManager.SetPowered boolean:[状态]
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`
- `[状态]`：布尔值，`true`表示开启，`false`表示关闭

**示例**：
```bash
# 开启SIM卡电源
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.SimManager.SetPowered boolean:true

# 关闭SIM卡电源
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.SimManager.SetPowered boolean:false
```

## 频段锁定和查询

### 频段查询和锁定实现细节

根据对libofono.so的代码分析，频段查询和锁定功能是通过D-Bus接口与`org.ofono.RadioSettings`服务通信实现的。以下是具体实现细节：

#### 获取网络频段信息
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.RadioSettings.GetBandInfo
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`

**实现细节**：
- 内部调用`org.ofono.RadioSettings`接口的`GetBandInfo`方法
- 不需要额外参数
- 返回当前支持的频段信息和已锁定的频段

### 锁定频段的两种方案

根据对libofono.so的深入分析，发现有两种锁定频段的方式：字符串方式和二进制字符串方式。

#### 方案一：使用字符串方式锁定频段

这是最常见的方式，通过指定频段的字符串名称进行锁定：

##### 锁定GSM频段
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.RadioSettings.LockBand string:"GSM" variant:string:"[频段]"
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`
- `[频段]`：字符串，表示GSM频段，例如：
  - `"850"` - 850MHz频段
  - `"900"` - 900MHz频段
  - `"1800"` - 1800MHz频段
  - `"1900"` - 1900MHz频段
  - `"850,900,1800,1900"` - 多个频段组合

**示例**：
```bash
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.RadioSettings.LockBand string:"GSM" variant:string:"900,1800"
```

##### 锁定WCDMA频段
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.RadioSettings.LockBand string:"WCDMA" variant:string:"[频段]"
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`
- `[频段]`：字符串，表示WCDMA频段，例如：
  - `"1"` - 2100MHz (Band I)
  - `"2"` - 1900MHz (Band II)
  - `"5"` - 850MHz (Band V)
  - `"8"` - 900MHz (Band VIII)
  - `"1,5,8"` - 多个频段组合

**示例**：
```bash
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.RadioSettings.LockBand string:"WCDMA" variant:string:"1,8"
```

##### 锁定LTE频段
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.RadioSettings.LockBand string:"LTE" variant:string:"[频段]"
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`
- `[频段]`：字符串，表示LTE频段，例如：
  - `"1"` - 2100MHz (Band 1)
  - `"3"` - 1800MHz (Band 3)
  - `"7"` - 2600MHz (Band 7)
  - `"20"` - 800MHz (Band 20)
  - `"1,3,7,20"` - 多个频段组合

**示例**：
```bash
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.RadioSettings.LockBand string:"LTE" variant:string:"1,3"
```

##### 锁定NR (5G) 频段（二进制方式）
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.RadioSettings.LockBand string:"NR" variant:string:"[二进制频段掩码]"
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`
- `[二进制频段掩码]`：二进制字符串，表示NR频段掩码，从右向左读：
  - 根据您的GetBandInfo输出，支持的NR频段为n28、n41和n78等

**示例**：
```bash
# 锁定n5、n8、n13、n20、n28频段
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.RadioSettings.LockBand string:"NR" variant:string:"000010001000010100001000000000000"
```

**注意**：上面的二进制字符串`"000010001000010100001000000000000"`锁定了以下NR频段（从右向左读）：
- n5（850MHz）：主要用于北美地区
- n8（900MHz）：主要用于欧洲和亚洲
- n13（700MHz）：主要用于北美地区
- n20（800MHz）：主要用于欧洲
- n28（700MHz）：主要用于亚太、欧洲和拉美地区

#### 方案二：使用二进制字符串方式锁定频段

这种方式使用二进制字符串表示频段，每个位置表示一个特定频段（0表示不锁定，1表示锁定）：

##### 锁定GSM频段（二进制方式）
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.RadioSettings.LockBand string:"GSM" variant:string:"[二进制频段掩码]"
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`
- `[二进制频段掩码]`：二进制字符串，表示GSM频段掩码，例如：
  - `"1000"` - 850MHz频段 (GSM 850)
  - `"0100"` - 900MHz频段 (GSM 900)
  - `"0010"` - 1800MHz频段 (GSM 1800)
  - `"0001"` - 1900MHz频段 (GSM 1900)
  - `"1111"` - 所有GSM频段 (850+900+1800+1900)

**示例**：
```bash
# 锁定GSM 900和1800频段
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.RadioSettings.LockBand string:"GSM" variant:string:"0110"
```

##### 锁定LTE频段（二进制方式）
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.RadioSettings.LockBand string:"LTE" variant:string:"[二进制频段掩码]"
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`
- `[二进制频段掩码]`：二进制字符串，表示LTE频段掩码，从右向左读：
  - 第1位表示Band 1 (2100MHz)
  - 第3位表示Band 3 (1800MHz)
  - 第5位表示Band 5 (850MHz)
  - 第8位表示Band 8 (900MHz)
  - 例如：`"00000101"` 表示锁定Band 1和Band 3

**示例**：
```bash
# 锁定LTE频段1和3
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.RadioSettings.LockBand string:"LTE" variant:string:"00000101"
```

##### 锁定NR (5G) 频段（二进制方式）
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.RadioSettings.LockBand string:"NR" variant:string:"[二进制频段掩码]"
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`
- `[二进制频段掩码]`：二进制字符串，表示NR频段掩码，从右向左读：
  - 根据您的GetBandInfo输出，支持的NR频段为n28、n41和n78等

**示例**：
```bash
# 锁定n5、n8、n13、n20、n28频段
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.RadioSettings.LockBand string:"NR" variant:string:"000010001000010100001000000000000"
```
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.RadioSettings.LockBand string:"NR" variant:string:"000001000000000000000000000000000"


**注意**：上面的二进制字符串`"000010001000010100001000000000000"`锁定了以下NR频段（从右向左读）：
- n5（850MHz）：主要用于北美地区
- n8（900MHz）：主要用于欧洲和亚洲
- n13（700MHz）：主要用于北美地区
- n20（800MHz）：主要用于欧洲
- n28（700MHz）：主要用于亚太、欧洲和拉美地区

### 查询频点信息
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.RadioSettings.GetFreqSpot string:"[网络类型]"
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`
- `[网络类型]`：字符串，表示网络类型，可以是以下值之一：
  - `"GSM"` - GSM网络
  - `"WCDMA"` - WCDMA网络
  - `"LTE"` - LTE网络
  - `"NR"` - 5G NR网络

**实现细节**：
- 内部构造参数格式为`(s)`，参数为网络类型字符串
- 调用`org.ofono.RadioSettings`接口的`GetFreqSpot`方法

**示例**：
```bash
# 查询LTE频点信息
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.RadioSettings.GetFreqSpot string:"LTE"
# 查询NR频点信息
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.RadioSettings.GetFreqSpot string:"NR"
```

### 数据连接上下文管理

### 获取所有上下文
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.ConnectionManager.GetContexts
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`

**返回值说明**：
- 返回所有数据连接上下文的路径和属性，每个上下文包含一个路径和属性集合

### 获取连接管理器状态
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.ConnectionManager.GetProperties
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`

**返回值说明**：
- `Attached`：布尔值，表示是否已附着到数据网络
- `RoamingAllowed`：布尔值，表示是否允许漫游
- `Powered`：布尔值，表示连接管理器是否已开启

### 获取特定上下文信息
```bash
dbus-send --system --print-reply --dest=org.ofono [上下文路径] org.ofono.ConnectionContext.GetProperties
```
**参数说明**：
- `[上下文路径]`：上下文路径，例如：`/ril_0/context1`

**返回值说明**：
- `AccessPointName`：字符串，APN名称
- `Type`：字符串，上下文类型（"internet"、"mms"等）
- `Protocol`：字符串，协议类型（"ip"、"ipv6"、"dual"）
- `Active`：布尔值，表示上下文是否激活
- `Settings`：字典，包含IP设置（如果上下文已激活）

### 激活上下文
```bash
dbus-send --system --print-reply --dest=org.ofono [上下文路径] org.ofono.ConnectionContext.SetProperty string:"Active" variant:boolean:true
```
**参数说明**：
- `[上下文路径]`：上下文路径，例如：`/ril_0/context1`

**示例**：
```bash
# 激活指定上下文
dbus-send --system --print-reply --dest=org.ofono /ril_0/context1 org.ofono.ConnectionContext.SetProperty string:"Active" variant:boolean:true
```

### 停用上下文
```bash
dbus-send --system --print-reply --dest=org.ofono [上下文路径] org.ofono.ConnectionContext.SetProperty string:"Active" variant:boolean:false
```
**参数说明**：
- `[上下文路径]`：上下文路径，例如：`/ril_0/context1`

**示例**：
```bash
# 停用指定上下文
dbus-send --system --print-reply --dest=org.ofono /ril_0/context1 org.ofono.ConnectionContext.SetProperty string:"Active" variant:boolean:false
```

### 停用所有上下文
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.ConnectionManager.DeactivateAll
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`

**示例**：
```bash
# 停用所有上下文
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.ConnectionManager.DeactivateAll
```

### 设置APN
```bash
dbus-send --system --print-reply --dest=org.ofono [上下文路径] org.ofono.ConnectionContext.SetProperty string:"AccessPointName" variant:string:"[APN名称]"
```
**参数说明**：
- `[上下文路径]`：上下文路径，例如：`/ril_0/context1`
- `[APN名称]`：字符串，要设置的APN名称，例如："cmnet"、"3gnet"等

**示例**：
```bash
# 设置APN为"cmnet"
dbus-send --system --print-reply --dest=org.ofono /ril_0/context1 org.ofono.ConnectionContext.SetProperty string:"AccessPointName" variant:string:"cmnet"
```

### 设置漫游允许状态
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.ConnectionManager.SetProperty string:"RoamingAllowed" variant:boolean:[状态]
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`
- `[状态]`：布尔值，`true`表示允许漫游，`false`表示禁止漫游

**示例**：
```bash
# 允许漫游
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.ConnectionManager.SetProperty string:"RoamingAllowed" variant:boolean:true

# 禁止漫游
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.ConnectionManager.SetProperty string:"RoamingAllowed" variant:boolean:false
```

## 设置命令（谨慎使用）

### 设置IMEI
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.Modem.SetIMEI string:"新IMEI号码"
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`
- `新IMEI号码`：字符串，15位数字的IMEI号码

**示例**：
```bash
# 设置IMEI为123456789012345
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.Modem.SetIMEI string:"123456789012345"
```

### 设置YSIMEI
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.Modem.SetYSIMEI string:"新YSIMEI号码"
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`
- `新YSIMEI号码`：字符串，YSIMEI号码

**示例**：
```bash
# 设置YSIMEI
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.Modem.SetYSIMEI string:"123456789012345"
```

### 设置软件版本号(SVN)
```bash
dbus-send --system --print-reply --dest=org.ofono [调制解调器路径] org.ofono.Modem.SetImeisv string:"新SVN号码"
```
**参数说明**：
- `[调制解调器路径]`：调制解调器路径，例如：`/ril_0`
- `新SVN号码`：字符串，软件版本号

**示例**：
```bash
# 设置SVN为01
dbus-send --system --print-reply --dest=org.ofono /ril_0 org.ofono.Modem.SetImeisv string:"01"
```

## 完整脚本

以下是自动获取所有信息的完整脚本：

```bash
#!/bin/bash
# 获取调制解调器路径并存储
MODEM_PATH=$(dbus-send --system --print-reply --dest=org.ofono / org.ofono.Manager.GetModems | grep object | head -1 | awk '{print $2}' | tr -d '"')
echo "调制解调器路径: $MODEM_PATH"

# 获取所有属性（包含序列号和IMEI）
echo "获取所有属性："
dbus-send --system --print-reply --dest=org.ofono $MODEM_PATH org.ofono.Modem.GetProperties

# 仅提取序列号和IMEI相关信息
echo "序列号和IMEI信息："
dbus-send --system --print-reply --dest=org.ofono $MODEM_PATH org.ofono.Modem.GetProperties | grep -A 1 "Serial\|IMEI\|Manufacturer\|Model\|Revision"

# 查询YSIMEI
echo "YSIMEI信息："
dbus-send --system --print-reply --dest=org.ofono $MODEM_PATH org.ofono.Modem.QueryYSIMEI

# 获取SIM卡信息
echo "SIM卡信息："
dbus-send --system --print-reply --dest=org.ofono $MODEM_PATH org.ofono.SimManager.GetProperties

# 获取SIM卡状态
echo "SIM卡状态："
dbus-send --system --print-reply --dest=org.ofono $MODEM_PATH org.ofono.SimManager.GetProperties | grep -A 1 "Present\|PinRequired\|SubscriberIdentity\|CardIdentifier"

# 获取网络注册信息
echo "网络注册信息："
dbus-send --system --print-reply --dest=org.ofono $MODEM_PATH org.ofono.NetworkRegistration.GetProperties

# 获取频段信息
echo "频段信息："
dbus-send --system --print-reply --dest=org.ofono $MODEM_PATH org.ofono.RadioSettings.GetBandInfo

# 获取数据连接上下文
echo "数据连接上下文："
dbus-send --system --print-reply --dest=org.ofono $MODEM_PATH org.ofono.ConnectionManager.GetContexts

# 获取连接管理器状态
echo "连接管理器状态："
dbus-send --system --print-reply --dest=org.ofono $MODEM_PATH org.ofono.ConnectionManager.GetProperties
```

将此脚本保存为 `get_device_info.sh`，然后执行 `chmod +x get_device_info.sh` 使其可执行，最后运行 `./get_device_info.sh` 即可。
