{"subs": [{"content": "proxies:\n    - cipher: chacha20-ietf-poly1305\n      name: \"\\U0001F1ED\\U0001F1F0香港1 | ⬇️ 2.6MB/s\"\n      password: 07a426bb-b7c9-4eb9-830d-d109e62083e9\n      port: 25601\n      server: *************\n      type: ss\n      udp: true\n    - cipher: aes-256-cfb\n      name: \"\\U0001F1F0\\U0001F1F7韩国1 | ⬇️ 1.2MB/s\"\n      password: yijian0503\n      port: 443\n      server: *************\n      type: ss\n      udp: true\n    - auth: e6d8d743-a9bc-48c9-be99-8f3cc94d16e1\n      name: \"\\U0001F1F8\\U0001F1EC新加坡1 | ⬇️ 1.5MB/s\"\n      password: e6d8d743-a9bc-48c9-be99-8f3cc94d16e1\n      port: 10086\n      server: asg.pagate.top\n      skip-cert-verify: false\n      sni: asg.pagate.top\n      type: hysteria2\n    - name: \"\\U0001F1ED\\U0001F1F0香港2 | ⬇️ 1.8MB/s\"\n      password: <password>\n      port: 8443\n      server: 85e272b6-sxe4g0-t1bnjq-1krtb.hkt.cdnhuawei.com\n      tls: true\n      type: http\n      udp: true\n      username: 60f6b4c4-9d70-11ed-a4d2-f23c9164ca5d\n    - auth: aaf7a87d-ef38-433e-b86e-07ddea14fac4\n      name: \"\\U0001F1FA\\U0001F1F8美国1 | ⬇️ 2.6MB/s\"\n      password: aaf7a87d-ef38-433e-b86e-07ddea14fac4\n      port: 55462\n      server: **************\n      skip-cert-verify: true\n      sni: www.bing.com\n      type: hysteria2\n    - auth: d558606f-7c22-4384-aebf-fb235dcea0ae\n      down: 50\n      name: \"\\U0001F1E9\\U0001F1EA德国1 | ⬇️ 1.2MB/s\"\n      password: d558606f-7c22-4384-aebf-fb235dcea0ae\n      port: 1443\n      server: de2.587458.xyz\n      skip-cert-verify: false\n      sni: de2.587458.xyz\n      type: hysteria2\n      udp: true\n      up: 50\n    - auth: e6d8d743-a9bc-48c9-be99-8f3cc94d16e1\n      name: \"\\U0001F1FA\\U0001F1F8美国2 | ⬇️ 2.5MB/s\"\n      password: e6d8d743-a9bc-48c9-be99-8f3cc94d16e1\n      port: 10086\n      server: aus.pagate.top\n      skip-cert-verify: false\n      sni: aus.pagate.top\n      type: hysteria2\n    - delay: 1711\n      fast-open: true\n      name: \"\\U0001F1F7\\U0001F1FA俄罗斯1 | ⬇️ 1.2MB/s\"\n      password: dongtaiwang.com\n      port: 30033\n      server: *************\n      skip-cert-verify: true\n      sni: www.bing.com\n      type: hysteria2\n    - client-fingerprint: chrome\n      name: \"\\U0001F1F8\\U0001F1EC新加坡2 | ⬇️ 1.1MB/s\"\n      network: ws\n      port: 443\n      server: ***************\n      servername: sgp.rtot.me\n      skip-cert-verify: true\n      tls: true\n      type: vless\n      udp: true\n      uuid: 0783b1b9-5d8b-4d03-adc3-b050acc71a29\n      ws-opts:\n        headers:\n            Host: sgp.rtot.me\n        path: /bing\n    - alpn:\n        - h3\n      auth_str: dongtaiwang.com\n      down: 1000 Mbps\n      name: \"\\U0001F1F7\\U0001F1FA俄罗斯2 | ⬇️ 1.8MB/s\"\n      obfs: \"\"\n      port: \"62003\"\n      protocol: \"\"\n      server: *************\n      skip-cert-verify: true\n      sni: \"\"\n      type: hysteria\n      up: 1000 Mbps\n    - alpn:\n        - h3\n      auth_str: dongtaiwang.com\n      down: \"100\"\n      name: \"\\U0001F1FA\\U0001F1F8美国3 | ⬇️ 2.4MB/s\"\n      obfs: \"\"\n      port: \"36194\"\n      protocol: udp\n      server: ************\n      skip-cert-verify: true\n      sni: apple.com\n      type: hysteria\n      up: \"100\"\n", "name": "sub", "remark": "subs-check专用,勿动", "source": "local", "process": [{"type": "Quick Setting Operator"}]}], "collections": [], "artifacts": [], "rules": [], "files": [{"name": "mihomo", "process": [{"args": {"content": "http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml", "mode": "link"}, "disabled": false, "type": "Script Operator"}], "remark": "subs-check专用,勿动", "source": "local", "sourceName": "sub", "sourceType": "subscription", "type": "mihomoProfile"}], "tokens": [], "schemaVersion": "2.0", "settings": {}, "modules": []}