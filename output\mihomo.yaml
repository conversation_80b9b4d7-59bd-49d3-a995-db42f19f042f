proxies:
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1ED\U0001F1F0香港1 | ⬇️ 2.6MB/s"
    password: 07a426bb-b7c9-4eb9-830d-d109e62083e9
    port: 25601
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F0\U0001F1F7韩国1 | ⬇️ 1.2MB/s"
    password: yijian0503
    port: 443
    server: *************
    type: ss
    udp: true
  - auth: e6d8d743-a9bc-48c9-be99-8f3cc94d16e1
    name: "\U0001F1F8\U0001F1EC新加坡1 | ⬇️ 1.5MB/s"
    password: e6d8d743-a9bc-48c9-be99-8f3cc94d16e1
    port: 10086
    server: asg.pagate.top
    skip-cert-verify: false
    sni: asg.pagate.top
    type: hysteria2
  - name: "\U0001F1ED\U0001F1F0香港2 | ⬇️ 1.8MB/s"
    password: <password>
    port: 8443
    server: 85e272b6-sxe4g0-t1bnjq-1krtb.hkt.cdnhuawei.com
    tls: true
    type: http
    udp: true
    username: 60f6b4c4-9d70-11ed-a4d2-f23c9164ca5d
    sni: 85e272b6-sxe4g0-t1bnjq-1krtb.hkt.cdnhuawei.com
  - auth: aaf7a87d-ef38-433e-b86e-07ddea14fac4
    name: "\U0001F1FA\U0001F1F8美国1 | ⬇️ 2.6MB/s"
    password: aaf7a87d-ef38-433e-b86e-07ddea14fac4
    port: 55462
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - auth: d558606f-7c22-4384-aebf-fb235dcea0ae
    down: 50
    name: "\U0001F1E9\U0001F1EA德国1 | ⬇️ 1.2MB/s"
    password: d558606f-7c22-4384-aebf-fb235dcea0ae
    port: 1443
    server: de2.587458.xyz
    skip-cert-verify: false
    sni: de2.587458.xyz
    type: hysteria2
    udp: true
    up: 50
  - auth: e6d8d743-a9bc-48c9-be99-8f3cc94d16e1
    name: "\U0001F1FA\U0001F1F8美国2 | ⬇️ 2.5MB/s"
    password: e6d8d743-a9bc-48c9-be99-8f3cc94d16e1
    port: 10086
    server: aus.pagate.top
    skip-cert-verify: false
    sni: aus.pagate.top
    type: hysteria2
  - delay: 1711
    fast-open: true
    name: "\U0001F1F7\U0001F1FA俄罗斯1 | ⬇️ 1.2MB/s"
    password: dongtaiwang.com
    port: 30033
    server: *************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡2 | ⬇️ 1.1MB/s"
    network: ws
    port: 443
    server: ***************
    skip-cert-verify: true
    tls: true
    type: vless
    udp: true
    uuid: 0783b1b9-5d8b-4d03-adc3-b050acc71a29
    ws-opts:
      headers:
        Host: sgp.rtot.me
      path: /bing
    servername: sgp.rtot.me
  - alpn:
      - h3
    auth_str: dongtaiwang.com
    down: 1000 Mbps
    name: "\U0001F1F7\U0001F1FA俄罗斯2 | ⬇️ 1.8MB/s"
    obfs: ''
    port: 62003
    protocol: ''
    server: *************
    skip-cert-verify: true
    sni: ''
    type: hysteria
    up: 1000 Mbps
    disable-sni: true
    auth-str: dongtaiwang.com
  - alpn:
      - h3
    auth_str: dongtaiwang.com
    down: '100'
    name: "\U0001F1FA\U0001F1F8美国3 | ⬇️ 2.4MB/s"
    obfs: ''
    port: 36194
    protocol: udp
    server: ************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria
    up: '100'
    auth-str: dongtaiwang.com
proxy-groups:
  - name: "\U0001F680 节点选择"
    type: select
    proxies:
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F680 手动切换"
    include-all: true
    type: select
  - name: ♻️ 自动选择
    type: url-test
    include-all: true
    interval: 300
    tolerance: 50
  - name: "\U0001F4F2 电报消息"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F4AC Ai平台"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F4F9 油管视频"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F3A5 奈飞视频"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - "\U0001F3A5 奈飞节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F4FA 巴哈姆特"
    type: select
    proxies:
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F680 节点选择"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F4FA 哔哩哔哩"
    type: select
    proxies:
      - "\U0001F3AF 全球直连"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
  - name: "\U0001F30D 国外媒体"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
      - DIRECT
  - name: "\U0001F30F 国内媒体"
    type: select
    proxies:
      - DIRECT
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F680 手动切换"
  - name: "\U0001F4E2 谷歌FCM"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: Ⓜ️ 微软Bing
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: Ⓜ️ 微软云盘
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: Ⓜ️ 微软服务
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - DIRECT
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: "\U0001F34E 苹果服务"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: "\U0001F3AE 游戏平台"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: "\U0001F3B6 网易音乐"
    type: select
    include-all: true
    filter: (?i)网易|音乐|NetEase|Music
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
  - name: "\U0001F3AF 全球直连"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
  - name: "\U0001F6D1 广告拦截"
    type: select
    proxies:
      - REJECT
      - DIRECT
  - name: "\U0001F343 应用净化"
    type: select
    proxies:
      - REJECT
      - DIRECT
  - name: "\U0001F41F 漏网之鱼"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - ♻️ 自动选择
      - DIRECT
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1E8\U0001F1F3 台湾节点"
      - "\U0001F1F8\U0001F1EC 狮城节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1FA\U0001F1F2 美国节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F680 手动切换"
  - name: "\U0001F1ED\U0001F1F0 香港节点"
    include-all: true
    filter: (?i)港|HK|hk|Hong Kong|HongKong|hongkong
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F1EF\U0001F1F5 日本节点"
    include-all: true
    filter: (?i)日本|川日|东京|大阪|泉日|埼玉|沪日|深日|JP|Japan
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F1FA\U0001F1F2 美国节点"
    include-all: true
    filter: (?i)美|波特兰|达拉斯|俄勒冈|凤凰城|费利蒙|硅谷|拉斯维加斯|洛杉矶|圣何塞|圣克拉拉|西雅图|芝加哥|US|United States
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F1E8\U0001F1F3 台湾节点"
    include-all: true
    filter: (?i)台|新北|彰化|TW|Taiwan
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F1F8\U0001F1EC 狮城节点"
    include-all: true
    filter: (?i)新加坡|坡|狮城|SG|Singapore
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F1F0\U0001F1F7 韩国节点"
    include-all: true
    filter: (?i)KR|Korea|KOR|首尔|韩|韓
    type: url-test
    interval: 300
    tolerance: 50
  - name: "\U0001F3A5 奈飞节点"
    include-all: true
    filter: (?i)NF|奈飞|解锁|Netflix|NETFLIX|Media
    type: select
rule-providers:
  LocalAreaNetwork:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/LocalAreaNetwork.list
    path: ./ruleset/LocalAreaNetwork.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  UnBan:
    url: 'https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/UnBan.list'
    path: ./ruleset/UnBan.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  BanAD:
    url: 'https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/BanAD.list'
    path: ./ruleset/BanAD.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  BanProgramAD:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/BanProgramAD.list
    path: ./ruleset/BanProgramAD.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  GoogleFCM:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/GoogleFCM.list
    path: ./ruleset/GoogleFCM.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  GoogleCN:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/GoogleCN.list
    path: ./ruleset/GoogleCN.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  SteamCN:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/SteamCN.list
    path: ./ruleset/SteamCN.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Bing:
    url: 'https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Bing.list'
    path: ./ruleset/Bing.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  OneDrive:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/OneDrive.list
    path: ./ruleset/OneDrive.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Microsoft:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Microsoft.list
    path: ./ruleset/Microsoft.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Apple:
    url: 'https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Apple.list'
    path: ./ruleset/Apple.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Telegram:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Telegram.list
    path: ./ruleset/Telegram.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  AI:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/AI.list
    path: ./ruleset/AI.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  OpenAi:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/OpenAi.list
    path: ./ruleset/OpenAi.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  NetEaseMusic:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/NetEaseMusic.list
    path: ./ruleset/NetEaseMusic.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Epic:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Epic.list
    path: ./ruleset/Epic.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Origin:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Origin.list
    path: ./ruleset/Origin.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Sony:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Sony.list
    path: ./ruleset/Sony.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Steam:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Steam.list
    path: ./ruleset/Steam.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Nintendo:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Nintendo.list
    path: ./ruleset/Nintendo.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  YouTube:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/YouTube.list
    path: ./ruleset/YouTube.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Netflix:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Netflix.list
    path: ./ruleset/Netflix.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Bahamut:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Bahamut.list
    path: ./ruleset/Bahamut.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  BilibiliHMT:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/BilibiliHMT.list
    path: ./ruleset/BilibiliHMT.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Bilibili:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Bilibili.list
    path: ./ruleset/Bilibili.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  ChinaMedia:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ChinaMedia.list
    path: ./ruleset/ChinaMedia.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  ProxyMedia:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ProxyMedia.list
    path: ./ruleset/ProxyMedia.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  ProxyGFWlist:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ProxyGFWlist.list
    path: ./ruleset/ProxyGFWlist.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  ChinaDomain:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ChinaDomain.list
    path: ./ruleset/ChinaDomain.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  ChinaCompanyIp:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ChinaCompanyIp.list
    path: ./ruleset/ChinaCompanyIp.list
    behavior: classical
    interval: 86400
    format: text
    type: http
  Download:
    url: >-
      https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Download.list
    path: ./ruleset/Download.list
    behavior: classical
    interval: 86400
    format: text
    type: http
rules:
  - "PROCESS-NAME,subs-check.exe,\U0001F3AF 全球直连"
  - "PROCESS-NAME,subs-check,\U0001F3AF 全球直连"
  - "RULE-SET,LocalAreaNetwork,\U0001F3AF 全球直连"
  - "RULE-SET,UnBan,\U0001F3AF 全球直连"
  - "RULE-SET,BanAD,\U0001F6D1 广告拦截"
  - "RULE-SET,BanProgramAD,\U0001F343 应用净化"
  - "RULE-SET,GoogleFCM,\U0001F4E2 谷歌FCM"
  - "RULE-SET,GoogleCN,\U0001F3AF 全球直连"
  - "RULE-SET,SteamCN,\U0001F3AF 全球直连"
  - 'RULE-SET,Bing,Ⓜ️ 微软Bing'
  - 'RULE-SET,OneDrive,Ⓜ️ 微软云盘'
  - 'RULE-SET,Microsoft,Ⓜ️ 微软服务'
  - "RULE-SET,Apple,\U0001F34E 苹果服务"
  - "RULE-SET,Telegram,\U0001F4F2 电报消息"
  - "RULE-SET,AI,\U0001F4AC Ai平台"
  - "RULE-SET,NetEaseMusic,\U0001F3B6 网易音乐"
  - "RULE-SET,Epic,\U0001F3AE 游戏平台"
  - "RULE-SET,Origin,\U0001F3AE 游戏平台"
  - "RULE-SET,Sony,\U0001F3AE 游戏平台"
  - "RULE-SET,Steam,\U0001F3AE 游戏平台"
  - "RULE-SET,Nintendo,\U0001F3AE 游戏平台"
  - "RULE-SET,YouTube,\U0001F4F9 油管视频"
  - "RULE-SET,Netflix,\U0001F3A5 奈飞视频"
  - "RULE-SET,Bahamut,\U0001F4FA 巴哈姆特"
  - "RULE-SET,BilibiliHMT,\U0001F4FA 哔哩哔哩"
  - "RULE-SET,Bilibili,\U0001F4FA 哔哩哔哩"
  - "RULE-SET,ChinaMedia,\U0001F30F 国内媒体"
  - "RULE-SET,ProxyMedia,\U0001F30D 国外媒体"
  - "RULE-SET,ProxyGFWlist,\U0001F680 节点选择"
  - "RULE-SET,ChinaDomain,\U0001F3AF 全球直连"
  - "RULE-SET,ChinaCompanyIp,\U0001F3AF 全球直连"
  - "RULE-SET,Download,\U0001F3AF 全球直连"
  - "GEOIP,CN,\U0001F3AF 全球直连"
  - "MATCH,\U0001F41F 漏网之鱼"
