
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.47
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: Start migrating...
[sub-store] INFO: Migration complete!
[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] listening on :::8299
[sub-store] INFO: 正在创建订阅： sub
[sub-store] INFO: 正在创建文件：mihomo
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.47
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] listening on :::8299

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.47
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] listening on :::8299
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在更新文件：mihomo...
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.47
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] listening on :::8299
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在更新文件：mihomo...
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🌀其他2-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他2-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🌀其他2-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他2-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.47
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] listening on :::8299
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在更新文件：mihomo...
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.47
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] listening on :::8299
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在更新文件：mihomo...
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.47
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] listening on :::8299
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在更新文件：mihomo...
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.9MB/s JoKERRvpn_jokerRvPn
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.9MB/s JoKERRvpn_jokerRvPn
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.9MB/s JoKERRvpn_jokerRvPn
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.9MB/s JoKERRvpn_jokerRvPn
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 1.2MB/s JoKERRvpn_jokerRvPn
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 1.2MB/s JoKERRvpn_jokerRvPn
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 1.2MB/s JoKERRvpn_jokerRvPn
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 1.2MB/s JoKERRvpn_jokerRvPn
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国2 | ⬇️ 1.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国2 | ⬇️ 1.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国2 | ⬇️ 1.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国2 | ⬇️ 1.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🌀其他2-TR | ⬇️ 2.3MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🌀其他2-TR | ⬇️ 2.3MB/s SAVTEAM3
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🌀其他2-TR | ⬇️ 2.3MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🌀其他2-TR | ⬇️ 2.3MB/s SAVTEAM3
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🌀其他4-TR | ⬇️ 1.9MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🌀其他4-TR | ⬇️ 1.9MB/s SAVTEAM3
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🌀其他4-TR | ⬇️ 1.9MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🌀其他4-TR | ⬇️ 1.9MB/s SAVTEAM3
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.2MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国10 | ⬇️ 2.4MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.2MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国10 | ⬇️ 2.4MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.2MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国10 | ⬇️ 2.4MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.2MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国10 | ⬇️ 2.4MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国2 | ⬇️ 2.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 2.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.8MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国2 | ⬇️ 2.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 2.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.8MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国2 | ⬇️ 2.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 2.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.8MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国2 | ⬇️ 2.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 2.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.8MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🌀其他5-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.8MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.8MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国10 | ⬇️ 1.5MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他5-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.8MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.8MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国10 | ⬇️ 1.5MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🌀其他5-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.8MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.8MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国10 | ⬇️ 1.5MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他5-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.8MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.8MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国10 | ⬇️ 1.5MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他10-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.4MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国11 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他10-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.4MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国11 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他10-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.4MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国11 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他10-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.4MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国11 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他6-TR | ⬇️ 2.0MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.5MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他6-TR | ⬇️ 2.0MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.5MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.9MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他6-TR | ⬇️ 2.0MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.5MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他6-TR | ⬇️ 2.0MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.5MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.9MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他9-TR | ⬇️ 1.5MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.4MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 2.2MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.8MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他9-TR | ⬇️ 1.5MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.4MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 2.2MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.8MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他9-TR | ⬇️ 1.5MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.4MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 2.2MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.8MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他9-TR | ⬇️ 1.5MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.4MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 2.2MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.8MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他8-TR | ⬇️ 1.4MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.0MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他8-TR | ⬇️ 1.4MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.0MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.0MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他8-TR | ⬇️ 1.4MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.0MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.6MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他8-TR | ⬇️ 1.4MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.0MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.0MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 2.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.7MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他8-TR | ⬇️ 1.3MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国8 | ⬇️ 2.4MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国10 | ⬇️ 2.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 2.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.7MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他8-TR | ⬇️ 1.3MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国8 | ⬇️ 2.4MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国10 | ⬇️ 2.9MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 2.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.7MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他8-TR | ⬇️ 1.3MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国8 | ⬇️ 2.4MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国10 | ⬇️ 2.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 2.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.7MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他8-TR | ⬇️ 1.3MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国8 | ⬇️ 2.4MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国10 | ⬇️ 2.9MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.5MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 3.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他12-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.5MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 3.5MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.5MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 3.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他12-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.5MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 3.5MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.5MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 3.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他12-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.5MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 3.5MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.5MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 3.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他12-TR | ⬇️ 1.6MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.5MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 3.5MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他9-TR | ⬇️ 2.0MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.7MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.5MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他9-TR | ⬇️ 2.0MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.7MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.5MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他9-TR | ⬇️ 2.0MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.7MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.5MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他9-TR | ⬇️ 2.0MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.7MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 1.5MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.7MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 3.6MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.5MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.7MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 3.6MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.5MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.7MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 3.6MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.5MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.7MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 1.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国6 | ⬇️ 3.6MB/s Parsashonam-251
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国7 | ⬇️ 1.5MB/s TELEGRAM-NUFiLTER
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他1-TR | ⬇️ 1.2MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他1-TR | ⬇️ 1.2MB/s SAVTEAM3
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他1-TR | ⬇️ 1.2MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 1.9MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他1-TR | ⬇️ 1.2MB/s SAVTEAM3
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 2.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 2.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 2.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 2.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.8MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.8MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.8MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国5 | ⬇️ 2.8MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.4MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.4MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.4MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 1.4MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国4 | ⬇️ 2.1MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 5.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 5.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 5.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国3 | ⬇️ 5.0MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🌀其他2-TR | ⬇️ 1.5MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 1.2MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他2-TR | ⬇️ 1.5MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 1.2MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🌀其他2-TR | ⬇️ 1.5MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 1.2MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🌀其他2-TR | ⬇️ 1.5MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国1 | ⬇️ 1.2MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国2 | ⬇️ 5.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国2 | ⬇️ 5.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国2 | ⬇️ 5.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: UUID may be invalid: 🇩🇪德国2 | ⬇️ 5.3MB/s TELEGRAM-NUFiLTER
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🌀其他4-TR | ⬇️ 1.7MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🌀其他4-TR | ⬇️ 1.7MB/s SAVTEAM3
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: UUID may be invalid: 🌀其他4-TR | ⬇️ 1.7MB/s SAVTEAM3
[sub-store] ERROR: UUID may be invalid: 🌀其他4-TR | ⬇️ 1.7MB/s SAVTEAM3
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: 使用缓存: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml, clash.meta
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.47
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] listening on :::8299

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.47
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] listening on :::8299

┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅
     Sub-Store -- v2.19.47
┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅┅

[sub-store] INFO: [BACKEND] body JSON limit: 10mb
[sub-store] INFO: [BACKEND] listening on :::8299
[sub-store] INFO: 正在更新订阅： sub
[sub-store] INFO: 正在更新文件：mihomo...
[sub-store] INFO: 正在下载文件：mihomo
请求 User-Agent: Go-http-client/1.1
[sub-store] INFO: Downloading...
User-Agent: clash.meta
Timeout: 8000
Proxy: undefined
Insecure: false
Preprocess: undefined
URL: http://127.0.0.1:8199/sub/ACL4SSR_Online_Full.yaml
[sub-store] INFO: statusCode: 200
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform mihomo does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
[sub-store] INFO: 正在下载订阅：sub
请求 User-Agent: Go-http-client/1.1
请求 target: V2Ray
实际输出: V2Ray
[sub-store] ERROR: 获取版本号失败: TypeError: Cannot read properties of undefined (reading 'indexOf')
[sub-store] INFO: Pre-processor [Clash Pre-processor] activated
[sub-store] INFO: Clash Parser is activated
[sub-store] ERROR: Target platform V2Ray does not support sni off. Proxy's fields (sni, tls-fingerprint and skip-cert-verify) will be modified.
