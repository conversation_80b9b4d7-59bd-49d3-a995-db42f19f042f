{"sub-store-cached-resource": "{\"15c409df76067619c94045ccd1afd33c\":{\"time\":1749737337389,\"data\":\"proxy-groups:\\n  - name: 🚀 节点选择\\n    type: select\\n    proxies:\\n      - ♻️ 自动选择\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇺🇲 美国节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n      - DIRECT\\n  - name: 🚀 手动切换\\n    include-all: true\\n    type: select\\n  - name: ♻️ 自动选择\\n    type: url-test\\n    include-all: true\\n    interval: 300\\n    tolerance: 50\\n  - name: 📲 电报消息\\n    type: select\\n    proxies:\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n      - 🇸🇬 狮城节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇯🇵 日本节点\\n      - 🇺🇲 美国节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n      - DIRECT\\n  - name: 💬 Ai平台\\n    type: select\\n    proxies:\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n      - 🇸🇬 狮城节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇯🇵 日本节点\\n      - 🇺🇲 美国节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n      - DIRECT\\n  - name: 📹 油管视频\\n    type: select\\n    proxies:\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n      - 🇸🇬 狮城节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇯🇵 日本节点\\n      - 🇺🇲 美国节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n      - DIRECT\\n  - name: 🎥 奈飞视频\\n    type: select\\n    proxies:\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n      - 🎥 奈飞节点\\n      - 🇸🇬 狮城节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇯🇵 日本节点\\n      - 🇺🇲 美国节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n      - DIRECT\\n  - name: 📺 巴哈姆特\\n    type: select\\n    proxies:\\n      - 🇨🇳 台湾节点\\n      - 🚀 节点选择\\n      - 🚀 手动切换\\n      - DIRECT\\n  - name: 📺 哔哩哔哩\\n    type: select\\n    proxies:\\n      - 🎯 全球直连\\n      - 🇨🇳 台湾节点\\n      - 🇭🇰 香港节点\\n  - name: 🌍 国外媒体\\n    type: select\\n    proxies:\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇺🇲 美国节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n      - DIRECT\\n  - name: 🌏 国内媒体\\n    type: select\\n    proxies:\\n      - DIRECT\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🚀 手动切换\\n  - name: 📢 谷歌FCM\\n    type: select\\n    proxies:\\n      - DIRECT\\n      - 🚀 节点选择\\n      - 🇺🇲 美国节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n  - name: Ⓜ️ 微软Bing\\n    type: select\\n    proxies:\\n      - DIRECT\\n      - 🚀 节点选择\\n      - 🇺🇲 美国节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n  - name: Ⓜ️ 微软云盘\\n    type: select\\n    proxies:\\n      - DIRECT\\n      - 🚀 节点选择\\n      - 🇺🇲 美国节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n  - name: Ⓜ️ 微软服务\\n    type: select\\n    proxies:\\n      - 🚀 节点选择\\n      - DIRECT\\n      - 🇺🇲 美国节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n  - name: 🍎 苹果服务\\n    type: select\\n    proxies:\\n      - DIRECT\\n      - 🚀 节点选择\\n      - 🇺🇲 美国节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n  - name: 🎮 游戏平台\\n    type: select\\n    proxies:\\n      - DIRECT\\n      - 🚀 节点选择\\n      - 🇺🇲 美国节点\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n  - name: 🎶 网易音乐\\n    type: select\\n    include-all: true\\n    filter: (?i)网易|音乐|NetEase|Music\\n    proxies:\\n      - DIRECT\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n  - name: 🎯 全球直连\\n    type: select\\n    proxies:\\n      - DIRECT\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n  - name: 🛑 广告拦截\\n    type: select\\n    proxies:\\n      - REJECT\\n      - DIRECT\\n  - name: 🍃 应用净化\\n    type: select\\n    proxies:\\n      - REJECT\\n      - DIRECT\\n  - name: 🐟 漏网之鱼\\n    type: select\\n    proxies:\\n      - 🚀 节点选择\\n      - ♻️ 自动选择\\n      - DIRECT\\n      - 🇭🇰 香港节点\\n      - 🇨🇳 台湾节点\\n      - 🇸🇬 狮城节点\\n      - 🇯🇵 日本节点\\n      - 🇺🇲 美国节点\\n      - 🇰🇷 韩国节点\\n      - 🚀 手动切换\\n  - name: 🇭🇰 香港节点\\n    include-all: true\\n    filter: (?i)港|HK|hk|Hong Kong|HongKong|hongkong\\n    type: url-test\\n    interval: 300\\n    tolerance: 50\\n  - name: 🇯🇵 日本节点\\n    include-all: true\\n    filter: (?i)日本|川日|东京|大阪|泉日|埼玉|沪日|深日|JP|Japan\\n    type: url-test\\n    interval: 300\\n    tolerance: 50\\n  - name: 🇺🇲 美国节点\\n    include-all: true\\n    filter: (?i)美|波特兰|达拉斯|俄勒冈|凤凰城|费利蒙|硅谷|拉斯维加斯|洛杉矶|圣何塞|圣克拉拉|西雅图|芝加哥|US|United States\\n    type: url-test\\n    interval: 300\\n    tolerance: 50\\n  - name: 🇨🇳 台湾节点\\n    include-all: true\\n    filter: (?i)台|新北|彰化|TW|Taiwan\\n    type: url-test\\n    interval: 300\\n    tolerance: 50\\n  - name: 🇸🇬 狮城节点\\n    include-all: true\\n    filter: (?i)新加坡|坡|狮城|SG|Singapore\\n    type: url-test\\n    interval: 300\\n    tolerance: 50\\n  - name: 🇰🇷 韩国节点\\n    include-all: true\\n    filter: (?i)KR|Korea|KOR|首尔|韩|韓\\n    type: url-test\\n    interval: 300\\n    tolerance: 50\\n  - name: 🎥 奈飞节点\\n    include-all: true\\n    filter: (?i)NF|奈飞|解锁|Netflix|NETFLIX|Media\\n    type: select\\n\\nrule-providers:\\n  LocalAreaNetwork:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/LocalAreaNetwork.list\\n    path: ./ruleset/LocalAreaNetwork.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  UnBan:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/UnBan.list\\n    path: ./ruleset/UnBan.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  BanAD:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/BanAD.list\\n    path: ./ruleset/BanAD.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  BanProgramAD:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/BanProgramAD.list\\n    path: ./ruleset/BanProgramAD.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  GoogleFCM:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/GoogleFCM.list\\n    path: ./ruleset/GoogleFCM.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  GoogleCN:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/GoogleCN.list\\n    path: ./ruleset/GoogleCN.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  SteamCN:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/SteamCN.list\\n    path: ./ruleset/SteamCN.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Bing:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Bing.list\\n    path: ./ruleset/Bing.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  OneDrive:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/OneDrive.list\\n    path: ./ruleset/OneDrive.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Microsoft:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Microsoft.list\\n    path: ./ruleset/Microsoft.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Apple:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Apple.list\\n    path: ./ruleset/Apple.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Telegram:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Telegram.list\\n    path: ./ruleset/Telegram.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  AI:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/AI.list\\n    path: ./ruleset/AI.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  OpenAi:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/OpenAi.list\\n    path: ./ruleset/OpenAi.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  NetEaseMusic:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/NetEaseMusic.list\\n    path: ./ruleset/NetEaseMusic.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Epic:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Epic.list\\n    path: ./ruleset/Epic.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Origin:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Origin.list\\n    path: ./ruleset/Origin.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Sony:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Sony.list\\n    path: ./ruleset/Sony.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Steam:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Steam.list\\n    path: ./ruleset/Steam.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Nintendo:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Nintendo.list\\n    path: ./ruleset/Nintendo.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  YouTube:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/YouTube.list\\n    path: ./ruleset/YouTube.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Netflix:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Netflix.list\\n    path: ./ruleset/Netflix.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Bahamut:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Bahamut.list\\n    path: ./ruleset/Bahamut.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  BilibiliHMT:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/BilibiliHMT.list\\n    path: ./ruleset/BilibiliHMT.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Bilibili:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Ruleset/Bilibili.list\\n    path: ./ruleset/Bilibili.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  ChinaMedia:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ChinaMedia.list\\n    path: ./ruleset/ChinaMedia.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  ProxyMedia:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ProxyMedia.list\\n    path: ./ruleset/ProxyMedia.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  ProxyGFWlist:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ProxyGFWlist.list\\n    path: ./ruleset/ProxyGFWlist.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  ChinaDomain:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ChinaDomain.list\\n    path: ./ruleset/ChinaDomain.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  ChinaCompanyIp:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/ChinaCompanyIp.list\\n    path: ./ruleset/ChinaCompanyIp.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n  Download:\\n    url: https://testingcf.jsdelivr.net/gh/ACL4SSR/ACL4SSR@master/Clash/Download.list\\n    path: ./ruleset/Download.list\\n    behavior: classical\\n    interval: 86400\\n    format: text\\n    type: http\\n\\nrules:\\n  - \\\"PROCESS-NAME,subs-check.exe,🎯 全球直连\\\"\\n  - \\\"PROCESS-NAME,subs-check,🎯 全球直连\\\"\\n  - \\\"RULE-SET,LocalAreaNetwork,🎯 全球直连\\\"\\n  - \\\"RULE-SET,UnBan,🎯 全球直连\\\"\\n  - \\\"RULE-SET,BanAD,🛑 广告拦截\\\"\\n  - \\\"RULE-SET,BanProgramAD,🍃 应用净化\\\"\\n  - \\\"RULE-SET,GoogleFCM,📢 谷歌FCM\\\"\\n  - \\\"RULE-SET,GoogleCN,🎯 全球直连\\\"\\n  - \\\"RULE-SET,SteamCN,🎯 全球直连\\\"\\n  - \\\"RULE-SET,Bing,Ⓜ️ 微软Bing\\\"\\n  - \\\"RULE-SET,OneDrive,Ⓜ️ 微软云盘\\\"\\n  - \\\"RULE-SET,Microsoft,Ⓜ️ 微软服务\\\"\\n  - \\\"RULE-SET,Apple,🍎 苹果服务\\\"\\n  - \\\"RULE-SET,Telegram,📲 电报消息\\\"\\n  - \\\"RULE-SET,AI,💬 Ai平台\\\"\\n  - \\\"RULE-SET,NetEaseMusic,🎶 网易音乐\\\"\\n  - \\\"RULE-SET,Epic,🎮 游戏平台\\\"\\n  - \\\"RULE-SET,Origin,🎮 游戏平台\\\"\\n  - \\\"RULE-SET,Sony,🎮 游戏平台\\\"\\n  - \\\"RULE-SET,Steam,🎮 游戏平台\\\"\\n  - \\\"RULE-SET,Nintendo,🎮 游戏平台\\\"\\n  - \\\"RULE-SET,YouTube,📹 油管视频\\\"\\n  - \\\"RULE-SET,Netflix,🎥 奈飞视频\\\"\\n  - \\\"RULE-SET,Bahamut,📺 巴哈姆特\\\"\\n  - \\\"RULE-SET,BilibiliHMT,📺 哔哩哔哩\\\"\\n  - \\\"RULE-SET,Bilibili,📺 哔哩哔哩\\\"\\n  - \\\"RULE-SET,ChinaMedia,🌏 国内媒体\\\"\\n  - \\\"RULE-SET,ProxyMedia,🌍 国外媒体\\\"\\n  - \\\"RULE-SET,ProxyGFWlist,🚀 节点选择\\\"\\n  - \\\"RULE-SET,ChinaDomain,🎯 全球直连\\\"\\n  - \\\"RULE-SET,ChinaCompanyIp,🎯 全球直连\\\"\\n  - \\\"RULE-SET,Download,🎯 全球直连\\\"\\n  - \\\"GEOIP,CN,🎯 全球直连\\\"\\n  - \\\"MATCH,🐟 漏网之鱼\\\"\\n\"}}", "sub-store-chr-expiration-time": "6e4", "sub-store-cached-headers-resource": "{}", "sub-store-csr-expiration-time": "1728e5", "sub-store-cached-script-resource": "{}"}